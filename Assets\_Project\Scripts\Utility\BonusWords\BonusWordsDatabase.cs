using System.Collections.Generic;
using UnityEngine;

[CreateAssetMenu(menuName = "Scriptable Objects/Simple Bonus Words Database", fileName = "SimpleBonusWordsDatabase")]
public class SimpleBonusWordsDatabase : ScriptableObject
{
    [SerializeField] private List<string> bonusWords = new List<string>();

    // Кэш для быстрого поиска
    private HashSet<string> _wordCache;
    private bool _cacheInitialized = false;

    /// <summary>
    /// Получить все бонусные слова
    /// </summary>
    public List<string> BonusWords => bonusWords;

    /// <summary>
    /// Инициализация кэша для быстрого поиска
    /// </summary>
    private void InitializeCache()
    {
        if (_cacheInitialized) return;

        _wordCache = new HashSet<string>();

        foreach (var word in bonusWords)
        {
            _wordCache.Add(word.ToUpper());
        }

        _cacheInitialized = true;
    }

    /// <summary>
    /// Проверить, существует ли бонусное слово
    /// </summary>
    public bool ContainsWord(string word)
    {
        InitializeCache();
        return _wordCache.Contains(word.ToUpper());
    }

    /// <summary>
    /// Добавить бонусное слово
    /// </summary>
    public void AddBonusWord(string word)
    {
        string upperWord = word.ToUpper();

        if (!bonusWords.Contains(upperWord))
        {
            bonusWords.Add(upperWord);

            // Обновляем кэш
            if (_cacheInitialized)
            {
                _wordCache.Add(upperWord);
            }

            Debug.Log($"✅ Added bonus word: {upperWord}");
        }
        else
        {
            Debug.Log($"⚠️ Bonus word already exists: {upperWord}");
        }
    }

    /// <summary>
    /// Добавить несколько бонусных слов
    /// </summary>
    public void AddBonusWords(List<string> words)
    {
        int addedCount = 0;
        foreach (var word in words)
        {
            string upperWord = word.ToUpper();
            if (!bonusWords.Contains(upperWord))
            {
                bonusWords.Add(upperWord);

                // Обновляем кэш
                if (_cacheInitialized)
                {
                    _wordCache.Add(upperWord);
                }
                addedCount++;
            }
        }

        Debug.Log($"🎯 Added {addedCount} new bonus words");
    }

    /// <summary>
    /// Получить количество бонусных слов
    /// </summary>
    public int GetBonusWordsCount()
    {
        return bonusWords.Count;
    }

    /// <summary>
    /// Очистить все бонусные слова
    /// </summary>
    public void ClearAllBonusWords()
    {
        bonusWords.Clear();
        _wordCache?.Clear();
        _cacheInitialized = false;

        Debug.Log("🧹 Cleared all bonus words");
    }
}
