using System;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;

[Serializable]
public class BonusWordEntry
{
    [SerializeField] private string word;
    [SerializeField] private int levelNumber;
    [SerializeField] private int matrixIndex;
    [SerializeField] private int length;
    [SerializeField] private string source; // "Local" или "External"
    
    public string Word { get => word; set => word = value; }
    public int LevelNumber { get => levelNumber; set => levelNumber = value; }
    public int MatrixIndex { get => matrixIndex; set => matrixIndex = value; }
    public int Length { get => length; set => length = value; }
    public string Source { get => source; set => source = value; }
    
    public BonusWordEntry(string word, int levelNumber, int matrixIndex, string source = "Local")
    {
        this.word = word;
        this.levelNumber = levelNumber;
        this.matrixIndex = matrixIndex;
        this.length = word.Length;
        this.source = source;
    }
}

[CreateAssetMenu(menuName = "Scriptable Objects/Bonus Words Database", fileName = "BonusWordsDatabase")]
public class BonusWordsDatabase : ScriptableObject
{
    [SerializeField] private List<BonusWordEntry> bonusWords = new List<BonusWordEntry>();
    
    // Кэш для быстрого поиска
    private HashSet<string> _wordCache;
    private Dictionary<string, List<BonusWordEntry>> _wordToEntriesMap;
    private bool _cacheInitialized = false;
    
    /// <summary>
    /// Получить все бонусные слова
    /// </summary>
    public List<BonusWordEntry> BonusWords => bonusWords;
    
    /// <summary>
    /// Инициализация кэша для быстрого поиска
    /// </summary>
    private void InitializeCache()
    {
        if (_cacheInitialized) return;
        
        _wordCache = new HashSet<string>();
        _wordToEntriesMap = new Dictionary<string, List<BonusWordEntry>>();
        
        foreach (var entry in bonusWords)
        {
            string upperWord = entry.Word.ToUpper();
            _wordCache.Add(upperWord);
            
            if (!_wordToEntriesMap.ContainsKey(upperWord))
            {
                _wordToEntriesMap[upperWord] = new List<BonusWordEntry>();
            }
            _wordToEntriesMap[upperWord].Add(entry);
        }
        
        _cacheInitialized = true;
    }
    
    /// <summary>
    /// Проверить, существует ли бонусное слово
    /// </summary>
    public bool ContainsWord(string word)
    {
        InitializeCache();
        return _wordCache.Contains(word.ToUpper());
    }
    
    /// <summary>
    /// Получить все записи для конкретного слова
    /// </summary>
    public List<BonusWordEntry> GetWordEntries(string word)
    {
        InitializeCache();
        string upperWord = word.ToUpper();
        return _wordToEntriesMap.ContainsKey(upperWord) ? _wordToEntriesMap[upperWord] : new List<BonusWordEntry>();
    }
    
    /// <summary>
    /// Добавить бонусное слово
    /// </summary>
    public void AddBonusWord(string word, int levelNumber, int matrixIndex, string source = "Local")
    {
        string upperWord = word.ToUpper();
        
        // Проверяем, не существует ли уже такое слово для этого уровня и матрицы
        var existingEntry = bonusWords.FirstOrDefault(bw => 
            bw.Word.ToUpper() == upperWord && 
            bw.LevelNumber == levelNumber && 
            bw.MatrixIndex == matrixIndex);
            
        if (existingEntry == null)
        {
            var newEntry = new BonusWordEntry(upperWord, levelNumber, matrixIndex, source);
            bonusWords.Add(newEntry);
            
            // Обновляем кэш
            if (_cacheInitialized)
            {
                _wordCache.Add(upperWord);
                if (!_wordToEntriesMap.ContainsKey(upperWord))
                {
                    _wordToEntriesMap[upperWord] = new List<BonusWordEntry>();
                }
                _wordToEntriesMap[upperWord].Add(newEntry);
            }
            
            Debug.Log($"✅ Added bonus word: {upperWord} (Level {levelNumber}, Matrix {matrixIndex}, Source: {source})");
        }
        else
        {
            Debug.Log($"⚠️ Bonus word already exists: {upperWord} (Level {levelNumber}, Matrix {matrixIndex})");
        }
    }
    
    /// <summary>
    /// Добавить несколько бонусных слов из уровня
    /// </summary>
    public void AddBonusWordsFromLevel(List<BonusWordInfo> bonusWordInfos, int levelNumber)
    {
        foreach (var wordInfo in bonusWordInfos)
        {
            AddBonusWord(wordInfo.Word, levelNumber, wordInfo.MatrixIndex, wordInfo.Source);
        }
        
        Debug.Log($"🎯 Added {bonusWordInfos.Count} bonus words from level {levelNumber}");
    }
    
    /// <summary>
    /// Получить все бонусные слова для конкретного уровня
    /// </summary>
    public List<BonusWordEntry> GetBonusWordsForLevel(int levelNumber)
    {
        return bonusWords.Where(bw => bw.LevelNumber == levelNumber).ToList();
    }
    
    /// <summary>
    /// Получить только уникальные слова для конкретного уровня (без дубликатов)
    /// </summary>
    public List<string> GetUniqueBonusWordsForLevel(int levelNumber)
    {
        return bonusWords
            .Where(bw => bw.LevelNumber == levelNumber)
            .Select(bw => bw.Word)
            .Distinct()
            .ToList();
    }
    
    /// <summary>
    /// Получить все уникальные бонусные слова (для глобального поиска)
    /// </summary>
    public List<string> GetAllUniqueBonusWords()
    {
        InitializeCache();
        return _wordCache.ToList();
    }
    
    /// <summary>
    /// Удалить все бонусные слова для конкретного уровня
    /// </summary>
    public void RemoveBonusWordsForLevel(int levelNumber)
    {
        int removedCount = bonusWords.RemoveAll(bw => bw.LevelNumber == levelNumber);
        
        if (removedCount > 0)
        {
            // Сбрасываем кэш для пересоздания
            _cacheInitialized = false;
            Debug.Log($"🗑️ Removed {removedCount} bonus words for level {levelNumber}");
        }
    }
    
    /// <summary>
    /// Очистить всю базу данных
    /// </summary>
    public void ClearAll()
    {
        bonusWords.Clear();
        _cacheInitialized = false;
        Debug.Log("🗑️ Cleared all bonus words from database");
    }
    
    /// <summary>
    /// Получить статистику базы данных
    /// </summary>
    public BonusWordsDatabaseStats GetStats()
    {
        InitializeCache();
        
        var levelGroups = bonusWords.GroupBy(bw => bw.LevelNumber).ToList();
        var sourceGroups = bonusWords.GroupBy(bw => bw.Source).ToList();
        
        return new BonusWordsDatabaseStats
        {
            TotalWords = bonusWords.Count,
            UniqueWords = _wordCache.Count,
            LevelsWithBonusWords = levelGroups.Count,
            LocalWords = sourceGroups.FirstOrDefault(g => g.Key == "Local")?.Count() ?? 0,
            ExternalWords = sourceGroups.FirstOrDefault(g => g.Key == "External")?.Count() ?? 0,
            AverageWordsPerLevel = levelGroups.Count > 0 ? (float)bonusWords.Count / levelGroups.Count : 0f
        };
    }
    
    /// <summary>
    /// Принудительно обновить кэш (например, после изменений в инспекторе)
    /// </summary>
    [ContextMenu("Refresh Cache")]
    public void RefreshCache()
    {
        _cacheInitialized = false;
        InitializeCache();
        Debug.Log($"🔄 Cache refreshed. Total unique words: {_wordCache.Count}");
    }
}

[Serializable]
public class BonusWordsDatabaseStats
{
    public int TotalWords;
    public int UniqueWords;
    public int LevelsWithBonusWords;
    public int LocalWords;
    public int ExternalWords;
    public float AverageWordsPerLevel;
}
