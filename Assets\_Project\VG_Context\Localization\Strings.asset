%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 20bb953c16467c4428907c08c1e259f6, type: 3}
  m_Name: Strings
  m_EditorClassIdentifier: 
  _translations:
  - key: level
    ru: "\u0423\u0420.\r"
    en: 
    tr: 
  - key: next
    ru: "\u0414\u0430\u043B\u0435\u0435\r"
    en: 
    tr: 
  - key: restart
    ru: "\u0417\u0430\u043D\u043E\u0432\u043E\r"
    en: 
    tr: 
  - key: win
    ru: "\u041F\u043E\u0431\u0435\u0434\u0430\r"
    en: 
    tr: 
  - key: lose
    ru: "\u041F\u043E\u0440\u0430\u0436\u0435\u043D\u0438\u0435\r"
    en: 
    tr: 
  - key: get_bonus
    ru: "\u0412\u043E\u0441\u043A\u0440\u0435\u0448\u0435\u043D\u0438\u0435\r"
    en: 
    tr: 
  - key: new_level
    ru: "\u041E\u0442\u043A\u0440\u044B\u0442 \u0441\u043B\u0435\u0434\u0443\u044E\u0449\u0438\u0439
      \u0443\u0440\u043E\u0432\u0435\u043D\u044C!\r"
    en: 
    tr: 
  - key: no_ads
    ru: "\u041E\u0442\u043A\u043B\u044E\u0447\u0435\u043D\u0438\u0435 \u0432\u0441\u043F\u043B\u044B\u0432\u0430\u044E\u0449\u0435\u0439
      \u0440\u0435\u043A\u043B\u0430\u043C\u044B\r"
    en: 
    tr: 
  - key: close
    ru: "\u0417\u0430\u043A\u0440\u044B\u0442\u044C\r"
    en: 
    tr: 
  - key: again
    ru: "\u0415\u0449\u0435 \u0440\u0430\u0437\r"
    en: 
    tr: 
  - key: skip
    ru: "\u041F\u0440\u043E\u043F\u0443\u0441\u0442\u0438\u0442\u044C\r"
    en: 
    tr: 
  - key: wrong_answer
    ru: "\u0421\u043B\u043E\u0432\u043E {WORD} \u043D\u0435 \u0437\u0430\u0433\u0430\u0434\u0430\u043D\u043E\r"
    en: 
    tr: 
  - key: booster
    ru: "\u0411\u0443\u0441\u0442\u0435\u0440\u044B\r"
    en: 
    tr: 
  - key: free
    ru: "\u0411\u0435\u0441\u043F\u043B\u0430\u0442\u043D\u043E X2\r"
    en: 
    tr: 
  - key: deal
    ru: "\u0412\u044B\u0433\u043E\u0434\u043D\u043E\u0435 \u043F\u0440\u0435\u0434\u043B\u043E\u0436\u0435\u043D\u0438\u0435\r"
    en: 
    tr: 
  - key: dis_ads
    ru: "\u041E\u0442\u043A\u043B\u044E\u0447\u0435\u043D\u0438\u0435 \u0440\u0435\u043A\u043B\u0430\u043C\u044B"
    en: 
    tr: 
