using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;
using VG;
using TMPro;
using System;
using System.Linq;

public class LevelManager : MonoBehaviour
{
    [Header("Settings")]
    [SerializeField] private float _victoryDelay = 0.3f;
    [SerializeField] private LevelsConfigSO levelsConfig;

    [Header("Services")]
    [SerializeField] private WordValidationService wordValidationService;

    [Header("Other")]
    //[SerializeField] private LevelsData levelsData;
    [SerializeField] private GridLayoutGroup wordsGrid;
    [SerializeField] private Letter letterPrefab;
    [SerializeField] private TextMeshProUGUI wrongTextInfo;
    //[SerializeField] private LevelsGenerator levelsGenerator;
    [SerializeField] private TutorialLevelBuilder _tutorial;
    [SerializeField] private GameObject _victoryWindow;
    [SerializeField] private LetterDotsConfig _letterDotsConfig;

    [Header("Answers")]

    [SerializeField] private RectTransform letterDotPrefab;
    //[SerializeField] private HorizontalLayoutGroup answerPrefab;
    //[SerializeField] private HorizontalLayoutGroup horizontalLinePrefab;
    [SerializeField] private Transform verticalLine;
    [SerializeField] private AnswersView answersView;
    //[SerializeField] int maxAnswerLength = 18;
    
    // ������������ ����� � ����� � �������� ��� �����

    private Dictionary<string, List<RectTransform>> wordsDot = new Dictionary<string, List<RectTransform>>();

    // ������������ ����� � ��� ���� � Letters

    private Dictionary<string, List<Letter>> wordsLetters = new Dictionary<string, List<Letter>>();

    private int currentLevel;
    private int currentRandomIndex;
    private LevelsSO currentLevelObject;
    private static LevelManager instance;
    private bool isWin = false;
    private List<Letter> currentLetters = new List<Letter>();
    private List<string> currentWords = new List<string>();

    private bool _hasTutorialCompleted;
    private bool _isTutorialCompletedInThisSession;


    public Letter[,] letterMatrix;

    public delegate void LevelAction();
    public static event LevelAction onVictory;
    public static event LevelAction onDefeat;
    public static event LevelAction onStart;

    public event Action<string> WordAdded;

    public static bool isInterShown = true;
    public static bool IsWin { get => instance.isWin; set => instance.isWin = value; }
    public static LevelManager Instance { get => instance; set => instance = value; }
    public static LevelsSO CurrentLevelObject { get => instance.currentLevelObject; set => instance.currentLevelObject = value; }
    public static GridLayoutGroup WordsGrid { get => instance.wordsGrid; set => instance.wordsGrid = value; }
    public static List<Letter> CurrentLetters { get => instance.currentLetters; set => instance.currentLetters = value; }
    public Letter LetterPrefab { get => letterPrefab; set => letterPrefab = value; }
    public static int CurrentLevel => instance.currentLevel;
    public static List<string> CurrentWords => instance.currentWords;

    public bool HasTutorialCompleted => _hasTutorialCompleted;

    public static Dictionary<string, List<Letter>> WordsLetters => instance.wordsLetters;

    public Dictionary<string, List<RectTransform>> WordsDot { get => wordsDot; set => wordsDot = value; }

    private void Awake()
    {
        if (instance == null)
        {
            instance = this;
        }
        else
        {
            Destroy(gameObject);
        }
    }
    private void OnDestroy()
    {
        if (instance == this)
        {
            instance = null;
        }

        // Отписываемся от событий
        WordValidationService.OnCorrectWordFound -= OnCorrectWordProcessed;
    }
    private void Start()
    {
        currentLevel = Saves.Int[Key_Save.level].Value;
        _hasTutorialCompleted = Saves.Bool[Key_Save.HasTutorialCompleted].Value;

        // Инициализируем сервис валидации слов
        if (wordValidationService == null)
        {
            wordValidationService = WordValidationService.Instance;
        }

        // Подписываемся на события сервиса
        WordValidationService.OnCorrectWordFound += OnCorrectWordProcessed;

        SoundPlayer.Instance.PlaySound(SoundActionType.BackgroundMusic);
        LoadLevel(currentLevel);
    }

    /// <summary>
    /// Обработка правильного слова
    /// </summary>
    private void OnCorrectWordProcessed(string word)
    {
        // Логика уже обработана в TryAddWord, здесь можно добавить дополнительные действия
        Debug.Log($"✅ Correct word processed in LevelManager: {word}");
    }
    private void Shuffle<T>(IList<T> list)
    {
        for (int i = list.Count - 1; i > 0; i--)
        {
            int j = UnityEngine.Random.Range(0, i + 1);
            (list[i], list[j]) = (list[j], list[i]);
        }
    }

    public static void Victory()
    {
        //instance.isWin = true;
        instance.StartCoroutine(instance.WaitForVictory());
    }

    private IEnumerator WaitForVictory()
    {
        _victoryWindow.gameObject.SetActive(true);
        yield return new WaitForSeconds(_victoryDelay);
        onVictory?.Invoke();
        //Saves.Commit();
    }

    public static void Defeat()
    {
        onDefeat?.Invoke();
        //Saves.Commit();
    }


    public static void LoadLevel(int level)
    {
        instance.currentWords.Clear();
        instance.ClearAnswers();
        instance.WordsDot.Clear();
        instance.isWin = false;
        ClearLetters();
        level = level - 1;
        //Saves.Int[Key_Save.level].Value = level + 1;
        if (level <= 0)
        {
            level = 0;
            Saves.Int[Key_Save.level].Value = 1;
        }
        // ������������ �������
        //if (level >= instance.levelsData.Levels.Count)
        //{
        //    level %= instance.levelsData.Levels.Count;
        //}
        instance.currentLevel = level;
        int baseCount = instance.levelsConfig.Levels.Count;
        const int CYCLE_START_LEVEL = 40;

        if (level < baseCount)
        {
            CurrentLevelObject = instance.levelsConfig.Levels[level];
        }
        else
        {
            int cycleLength = baseCount - CYCLE_START_LEVEL;
            if (cycleLength <= 0)
            {
                Debug.LogError($"Некорректная настройка: начальный уровень цикла ({CYCLE_START_LEVEL}) больше или равен общему количеству уровней ({baseCount})");
                CurrentLevelObject = instance.levelsConfig.Levels[baseCount - 1];
                return;
            }

            int overflow = level - baseCount;
            int cycleIndex = overflow % cycleLength;
            int actualLevelIndex = CYCLE_START_LEVEL + cycleIndex;

            CurrentLevelObject = instance.levelsConfig.Levels[actualLevelIndex];
            Debug.Log($"Циклический уровень: игровой уровень {level} → реальный уровень {actualLevelIndex} (цикл с {CYCLE_START_LEVEL}-го уровня)");
        }


        instance.currentWords = new List<string>(CurrentLevelObject.Words);
        //Debug.Log("level: " + level + " levelSave: " + Saves.Int[Key_Save.level].Value);
        /*foreach (string word in instance.currentWords)
        {
            Debug.Log(word);
        }*/

        WordsGrid.constraint = GridLayoutGroup.Constraint.FixedRowCount;
        WordsGrid.constraintCount = CurrentLevelObject.RowsCount;
        int rows = CurrentLevelObject.RowsCount;
        int cols = CurrentLevelObject.ColumnsCount;

        // ��������� ������ ����� LevelsGenerator
        if (instance._hasTutorialCompleted == true)
        {
            //instance.levelsGenerator.GenerateLevel(level + 1);
            instance.GenerateLevel(level - 1, rows, cols);
        }
        else
            instance._tutorial.Build();
        #region to do
        /*
        if (Saves.Int[Key_Save.level].Value <= 4)
        {
            isInterShown = false;
        }
        else
        {
            isInterShown = true;
        }
        if (Saves.Int[Key_Save.level].Value == 3)
        {
            Review.Request();
        }
        if (Saves.Int[Key_Save.level].Value == 1)
        {
            instance.tutor1.enabled = true;
            instance.tutor1.StartTutorial();
        }
        */
        #endregion
        onStart?.Invoke();
        Saves.Commit();
    }

    // ��������� ��� �������� ������
    public void GenerateLevel(int index, int rows, int cols)
    {
        currentRandomIndex = 0;
        Letter[,] newMatrix = new Letter[rows, cols];
        Debug.Log($"currentLevel {currentLevel}");
        for (int i = 0; i < rows; i++)
        {
            for (int j = 0; j < cols; j++)
            {
                Letter letterObj = Instantiate(instance.letterPrefab, WordsGrid.transform);
                newMatrix[i, j] = letterObj;
                FillMatrix matrix = currentLevelObject.FillMatrixes[currentRandomIndex];
                letterObj.SetNewLetter(matrix[i,j].GetCharLetter());
                letterObj.SetWordInfo(matrix[i, j].Word, matrix[i, j].IndexInWord);
            }
        }
        LoadLevelData(newMatrix, currentLevelObject.Words);
    }
    // ��������� ��� ������������� (����� ���� ������� �� ������)
    public void RegenerateLevel()
    {
        // �������� ������� �� ������
        currentRandomIndex = (++currentRandomIndex) % currentLevelObject.FillMatrixes.Count;
        Debug.Log($"New index: {currentRandomIndex}");
        FillMatrix newMatrix = currentLevelObject.FillMatrixes[currentRandomIndex];
        Letter[,] matrix = new Letter[newMatrix.RowCount, newMatrix.ColCount];
        for (int i = 0; i < newMatrix.RowCount; i++)
        {
            for (int j = 0; j < newMatrix.ColCount; j++)
            {
                LetterData data = newMatrix[i, j];
                // ����� Letter, �������������� ������� �����
                Letter letter = WordsLetters[data.Word][data.IndexInWord];
                // ������ � ����� �������
                matrix[i, j] = letter;

            }
        }
        RebuildLevel(matrix);
    }

    public void LoadLevelData(Letter[,] letterMatrix, List<string> words)
    {
        this.letterMatrix = letterMatrix;
        wordsLetters.Clear();
        ClearLetters(); // �������� ���������� ��������

        UpdateLetters();

        // ��������� ����� � ������ ����� �� �� �������

        foreach (var key in wordsLetters.Keys.ToList())
        {
            wordsLetters[key] = wordsLetters[key].OrderBy(letter => letter.WordIndex).ToList();
        }

        Debug.Log("Level data loaded with Letter prefabs.");

        currentWords = new List<string>(words);

        // Передаем слова в сервис валидации
        if (wordValidationService != null)
        {
            wordValidationService.SetLevelWords(words);
        }

        InitAnswers();
    }

    public void RebuildLevel(Letter[,] updatedMatrix)
    {
        Debug.Log("Rebuilding level!");
        // ��������� ���������� ������� ����
        this.letterMatrix = updatedMatrix;
        int rows = CurrentLevelObject.RowsCount;
        int cols = CurrentLevelObject.ColumnsCount;
        for (int row = 0; row < rows; row++)
        {
            for (int col = 0; col < cols; col++)
            {
                Letter letter = letterMatrix[row, col];
                letter.transform.SetAsLastSibling();
            }
        }
        currentLetters.Clear();
        UpdateLetters();
        Debug.Log("Level rebuilt successfully with updated matrix.");
    }

    private void UpdateLetters()
    {
        int rows = CurrentLevelObject.RowsCount;
        int cols = CurrentLevelObject.ColumnsCount;

        for (int row = 0; row < rows; row++)
        {
            for (int col = 0; col < cols; col++)
            {
                Letter letter = letterMatrix[row, col];
                letter.Rect.SetAsLastSibling();
                if (letter != null)
                {
                    string word = letter.WordReference;

                    // ���������� ����� �� ������
                    if (!wordsLetters.ContainsKey(word))
                    {
                        wordsLetters.Add(word, new List<Letter>());
                    }
                    wordsLetters[word].Add(letter);
                    currentLetters.Add(letter); // ��������� � ����� ������
                }
            }
        }
        
        int k = 0;
        for (int i = 0; i < rows; i++)
        {
            for (int j = 0; j < cols; j++)
            {
                CurrentLetters[k].Neighbors.Clear();
                if (IsInsideMatrix(i + 1, j))
                    CurrentLetters[k].Neighbors.Add(CurrentLetters[k + rows]);
                if (IsInsideMatrix(i, j + 1))
                    CurrentLetters[k].Neighbors.Add(CurrentLetters[k + 1]);
                if (IsInsideMatrix(i - 1, j))
                    CurrentLetters[k].Neighbors.Add(CurrentLetters[k - rows]);
                if (IsInsideMatrix(i, j - 1))
                    CurrentLetters[k].Neighbors.Add(CurrentLetters[k - 1]);
                k++;
            }

        }

        // '������� ������ �������?
        bool IsInsideMatrix(int r, int c) => r >= 0 && c >= 0 && r < rows && c < cols;
    }


    /// <summary>
    /// ���������� ������
    /// </summary>
    private void InitAnswers()
    {
        foreach (string word in instance.currentWords)
        {
            List<RectTransform> answers = new List<RectTransform>();
            foreach (char c in word)
            {
                RectTransform dot = Instantiate(letterDotPrefab, verticalLine.transform);
                answers.Add(dot);
            }
            WordsDot.Add(word, answers);
        }
        answersView.UpdateAnswers(Screen.width, Screen.height);
    }
    private void ClearAnswers()
    {
        answersView.ClearAnswers();
    }
    public static void ClearLetters()
    {
        for (int i = 0; i < CurrentLetters.Count; i++)
        {
            if (CurrentLetters[i] != null)
            {
                Destroy(CurrentLetters[i].gameObject);
            }
        }
        CurrentLetters.Clear();
    }


    public static void LoadNextLevel()
    {
        if (instance._isTutorialCompletedInThisSession == true)
        {
            LoadLevel(instance.currentLevel + 1);
            instance._isTutorialCompletedInThisSession = false;
            return;
        }
        
        if (!IsWin)
        {
            Saves.Int[Key_Save.level].Value += 1;
        }
        LoadLevel(Saves.Int[Key_Save.level].Value);

    }
    public static void LoadPrevLevel()
    {
        Saves.Int[Key_Save.level].Value -= 1;
        LoadLevel(Saves.Int[Key_Save.level].Value);


    }

    public static void ReloadLevel()
    {
        LoadLevel(Saves.Int[Key_Save.level].Value);
    }

    public void CompleteTutorial()
    {
        _hasTutorialCompleted = true;
        _isTutorialCompletedInThisSession = true;
    }

    public bool TryAddWord(string word, Letter[] letters)
    {
        if (IsWin || word.Length <= 1)
        {
            return false;
        }

        // Используем новый сервис валидации слов
        if (wordValidationService != null)
        {
            var result = wordValidationService.ValidateWord(word);

            if (result.Type == WordType.Correct)
            {
                // Обрабатываем правильное слово - анимация букв
                verticalLine.GetComponent<VerticalLayoutGroup>().enabled = false;

                instance.currentWords.Remove(word);

                int i = 0;
                foreach (Letter letter in letters)
                {
                    if (i >= instance.WordsDot[word].Count)
                        break;

                    Letter answerLetter = Instantiate(letter, letter.transform.position, Quaternion.identity);
                    answerLetter.transform.SetParent(letter.transform.parent);
                    answerLetter.LayoutElement.ignoreLayout = true;
                    answerLetter.Rect.localScale = letter.Rect.localScale;
                    answerLetter.Rect.sizeDelta = letterPrefab.Rect.sizeDelta;
                    instance.WordsDot[word][i].parent.parent.SetAsLastSibling();
                    answerLetter.AnswerDot = instance.WordsDot[word][i];
                    answerLetter.Text.color = letterPrefab.Text.color;
                    answerLetter.MoveToAnswers();
                    i++;
                }

                if (instance.currentWords.Count == 0)
                {
                    Victory();
                }

                WordAdded?.Invoke(word);

                // Обрабатываем через сервис (звук, события, логика)
                wordValidationService.ProcessWord(word, letters);
                return true;
            }
            else
            {
                // Обрабатываем неправильное или бонусное слово через сервис
                return wordValidationService.ProcessWord(word, letters);
            }
        }

        return false;
    }



    // �������, ������� ���������� ��������� ����� �� ������
    public string GetRandomWord(List<string> words)
    {
        if (words == null || words.Count == 0)
        {
            //throw new ArgumentException("The list of words cannot be null or empty.");
            return null;
        }

        int randomIndex = UnityEngine.Random.Range(0, words.Count);
        Debug.Log("random word: " + words[randomIndex]);
        return words[randomIndex];
    }


    // �������, ������� ���������� ������ ���� ���������� ����� �� ������
    public static List<Letter> GetRandomWordLetters()
    {
        List<Letter> letterList = new List<Letter>();
        string word = instance.GetRandomWord(instance.currentWords);
        return instance.wordsLetters[word];
        //return letterList;
    }


    //private void
}
