using System.Collections.Generic;
using UnityEngine;

[CreateAssetMenu(fileName = "SimpleBonusWordsDatabase", menuName = "Game/Simple Bonus Words Database")]
public class SimpleBonusWordsDatabase : ScriptableObject
{
    [SerializeField] private List<string> bonusWords = new List<string>();
    
    /// <summary>
    /// Добавляет бонусные слова в базу данных (избегает дубликатов)
    /// </summary>
    public void AddBonusWords(IEnumerable<string> words)
    {
        foreach (string word in words)
        {
            if (!string.IsNullOrEmpty(word) && !bonusWords.Contains(word))
            {
                bonusWords.Add(word.ToUpper());
            }
        }
    }
    
    /// <summary>
    /// Добавляет одно бонусное слово в базу данных
    /// </summary>
    public void AddBonusWord(string word)
    {
        if (!string.IsNullOrEmpty(word) && !bonusWords.Contains(word.ToUpper()))
        {
            bonusWords.Add(word.ToUpper());
        }
    }
    
    /// <summary>
    /// Проверяет, содержится ли слово в базе данных бонусных слов
    /// </summary>
    public bool ContainsBonusWord(string word)
    {
        return !string.IsNullOrEmpty(word) && bonusWords.Contains(word.ToUpper());
    }
    
    /// <summary>
    /// Возвращает количество бонусных слов в базе данных
    /// </summary>
    public int GetBonusWordsCount()
    {
        return bonusWords.Count;
    }
    
    /// <summary>
    /// Возвращает все бонусные слова (только для чтения)
    /// </summary>
    public IReadOnlyList<string> GetAllBonusWords()
    {
        return bonusWords.AsReadOnly();
    }
    
    /// <summary>
    /// Очищает все бонусные слова из базы данных
    /// </summary>
    public void ClearAllBonusWords()
    {
        bonusWords.Clear();
    }
}
