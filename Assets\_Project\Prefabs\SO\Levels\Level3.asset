%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: a980bed33a1e6964d9e431f3aff7ab6a, type: 3}
  m_Name: Level3
  m_EditorClassIdentifier: 
  levelNum: 3
  lettersCount: 3
  rowsCount: 3
  columnsCount: 3
  backwardsProbability: 0
  fillMatrixes:
  - rows:
    - row:
      - Letter: "\u0443"
        Word: "\u0443\u0445\u043E"
        IndexInWord: 0
      - Letter: "\u043C"
        Word: "\u043C\u0451\u0434"
        IndexInWord: 0
      - Letter: "\u0451"
        Word: "\u043C\u0451\u0434"
        IndexInWord: 1
    - row:
      - Letter: "\u0445"
        Word: "\u0443\u0445\u043E"
        IndexInWord: 1
      - Letter: "\u0438"
        Word: "\u0438\u043C\u044F"
        IndexInWord: 0
      - Letter: "\u0434"
        Word: "\u043C\u0451\u0434"
        IndexInWord: 2
    - row:
      - Letter: "\u043E"
        Word: "\u0443\u0445\u043E"
        IndexInWord: 2
      - Letter: "\u043C"
        Word: "\u0438\u043C\u044F"
        IndexInWord: 1
      - Letter: "\u044F"
        Word: "\u0438\u043C\u044F"
        IndexInWord: 2
  - rows:
    - row:
      - Letter: "\u0443"
        Word: "\u0443\u0445\u043E"
        IndexInWord: 0
      - Letter: "\u043C"
        Word: "\u043C\u0451\u0434"
        IndexInWord: 0
      - Letter: "\u0451"
        Word: "\u043C\u0451\u0434"
        IndexInWord: 1
    - row:
      - Letter: "\u0445"
        Word: "\u0443\u0445\u043E"
        IndexInWord: 1
      - Letter: "\u043E"
        Word: "\u0443\u0445\u043E"
        IndexInWord: 2
      - Letter: "\u0434"
        Word: "\u043C\u0451\u0434"
        IndexInWord: 2
    - row:
      - Letter: "\u0438"
        Word: "\u0438\u043C\u044F"
        IndexInWord: 0
      - Letter: "\u043C"
        Word: "\u0438\u043C\u044F"
        IndexInWord: 1
      - Letter: "\u044F"
        Word: "\u0438\u043C\u044F"
        IndexInWord: 2
  words:
  - "\u043C\u0451\u0434"
  - "\u0438\u043C\u044F"
  - "\u0443\u0445\u043E"
  bonusWords:
  - Word: "\u041C\u041E\u0425"
    MatrixIndex: 0
    Length: 3
  - Word: "\u041C\u041E\u0425"
    MatrixIndex: 1
    Length: 3
  - Word: "\u0423\u041C"
    MatrixIndex: 0
    Length: 2
  - Word: "\u0423\u041C"
    MatrixIndex: 1
    Length: 2
  - Word: "\u0423\u0425\u041E\u0414"
    MatrixIndex: 1
    Length: 4
  - Word: "\u042F\u0414"
    MatrixIndex: 0
    Length: 2
  - Word: "\u042F\u0414"
    MatrixIndex: 1
    Length: 2
